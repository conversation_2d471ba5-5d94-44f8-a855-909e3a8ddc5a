<script lang="ts">
	import { Timeline, TimelineItem, <PERSON>ge, Card, Button, Tabs, TabItem } from 'flowbite-svelte';
	import {
		ServerSolid,
		RefreshOutline,
		ExclamationCircleOutline,
		LayersSolid
	} from 'flowbite-svelte-icons';
	import { onMount } from 'svelte';
	import { CustomerService } from '$src/lib/api/features/customer/customers.service';
	import { t } from '$src/lib/stores/i18n';

	export let access_token: string;

	// Workflow configuration types
	interface WorkflowStep {
		id: string;
		name: string;
		type: string;
		description: string;
		depends_on?: string[];
		config: {
			endpoint: string;
			method: string;
			headers?: Record<string, string>;
			request_body?: Record<string, any>;
			response_extraction?: Record<string, any>;
			validation?: {
				rules: Array<{
					id: string;
					type: string;
					path?: string;
					operator?: string;
					value?: string;
					error_message: string;
					warning_only?: boolean;
				}>;
			};
			retry?: {
				max_attempts: string | number;
				delay_seconds: string | number;
			};
		};
	}

	interface WorkflowConfiguration {
		schema_version: string;
		workflow: {
			id: string;
			name: string;
			version: string;
			description: string;
			category: string;
			tags: string[];
		};
		configuration: {
			api: {
				base_url: string;
				timeout_seconds: number;
				ssl_verify: boolean;
				max_retries: number;
				retry_delay_seconds: number;
				backoff_strategy: string;
				endpoints: Record<string, string>;
			};
			response_fields: Record<string, string>;
			validation: Record<string, string>;
			error_messages: Record<string, string>;
			data_source: {
				mode: string;
				fallback_mode: string;
				database_queries?: Array<any>;
				fixed_values: Record<string, string>;
			};
			execution: {
				timeout_minutes: number;
				retry_policy: {
					default_max_retries: number;
					default_delay_seconds: number;
					backoff_strategy: string;
				};
				cache: {
					enabled: boolean;
					duration_minutes: number;
					key_template: string;
					environments: Record<string, { duration_minutes: number }>;
				};
			};
			environments: Record<string, {
				api: {
					base_url: string;
					ssl_verify: boolean;
					timeout_seconds: number;
				};
				cache: { duration_minutes: number };
				credentials: {
					username: string;
					password: string;
				};
			}>;
		};
		steps: WorkflowStep[];
		validation: {
			input_schema: Record<string, any>;
			business_rules: Array<any>;
		};
		metadata: {
			support_contact: string;
		};
	}

	// Workflow data stores
	let workflows: WorkflowConfiguration[] = [];
	let policyListWorkflow: WorkflowConfiguration | null = null; // Backward compatibility
	let policyDetailsWorkflow: WorkflowConfiguration | null = null; // Backward compatibility
	let loading = true;
	let error: string | null = null;

	// Initialize customer service
	const customerService = new CustomerService();

	// Function to fetch workflow configurations from backend
	async function fetchWorkflowConfigurations() {
		try {
			loading = true;
			error = null;

			const result = await customerService.getCustomerPolicyConfigurations(access_token);

			if (result.res_status === 200) {
				// Handle new array-based response
				workflows = result.workflows || [];
			} else {
				throw new Error(result.error_msg || 'Failed to load workflow configurations');
			}
		} catch (err) {
			console.error('Error fetching workflow configurations:', err);
			error = err instanceof Error ? err.message : 'Unknown error occurred';
		} finally {
			loading = false;
		}
	}

	// Load workflow configurations on component mount
	onMount(() => {
		fetchWorkflowConfigurations();
	});
</script>

<div class="space-y-10 rounded-lg bg-white p-6 shadow-md">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">{t('settings_policy_tab_title')}</h2>
			<p class="text-sm text-gray-500">{t('settings_policy_tab_description')}</p>
		</div>
	</div>

	<!-- Loading State -->
	{#if loading}
		<div class="flex items-center justify-center p-4 gap-3">
			<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
			<span class="text-sm text-gray-600">{t('settings_policy_workflow_loading')}</span>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex">
				<ExclamationCircleOutline class="h-5 w-5 text-red-800" />
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">{t('settings_policy_workflow_loading_error')}</h3>
					<div class="mt-2 text-sm text-red-700">
						<p>{error}</p>
					</div>
					<!-- <div class="mt-4">
						<Button color="blue" size="sm" on:click={fetchWorkflowConfigurations}>
							<RefreshOutline class="w-4 h-4 mr-2" />
							Retry
						</Button>
					</div> -->
				</div>
			</div>
		</div>
	{:else if workflows.length === 0}
		<!-- Empty State -->
		<div class="text-center p-4">
			<div>
				<!-- <h3 class="text-lg font-medium text-gray-900 mb-2">No Workflows Available</h3> -->
				<p class="text-sm text-gray-600">{t('settings_policy_workflow_empty')}</p>
				<!-- <Button color="blue" size="sm" on:click={fetchWorkflowConfigurations}>
					<RefreshOutline class="w-4 h-4 mr-2" />
					Refresh
				</Button> -->
			</div>
		</div>
	{:else}
		<!-- Dynamic Tabbed Interface -->
		<Tabs tabStyle="pill" contentClass="rounded-lg">
			{#each workflows as workflow, index}
				<TabItem open={index === 0} title={workflow.workflow.name}>
					<svelte:fragment slot="title">
						{index+ 1 }. {workflow.workflow.name}
					</svelte:fragment>

					<div class="space-y-8">
						<!-- WORKFLOW CONFIGURATION SECTION -->
						<div>
							<div class="flex items-center mb-4">
								<div class="bg-blue-100 p-2 rounded-lg mr-3">
									<ServerSolid class="w-6 h-6 text-blue-500" />
								</div>
								<div>
									<h3 class="text-xl font-bold text-gray-800">{t('settings_policy_workflow_configuration_title')}</h3>
									<!-- <p class="text-sm text-gray-600">Overall workflow settings and properties</p> -->
								</div>
							</div>

							<!-- Workflow Basic Info -->
							<!-- <div class="bg-white rounded-lg p-4 mb-4 shadow-sm">
								<h4 class="text-lg font-semibold text-gray-800 mb-2">{workflow.workflow.name}</h4>
								<p class="text-sm text-gray-600 mb-4">{workflow.workflow.description}</p>

								<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
									<div class="bg-blue-50 p-3 rounded-lg">
										<Badge color={colorScheme.badgeColor} class="mb-2">Version</Badge>
										<p class="text-gray-700 font-medium">{workflow.workflow.version}</p>
									</div>
									<div class="bg-green-50 p-3 rounded-lg">
										<Badge color="green" class="mb-2">Category</Badge>
										<p class="text-gray-700 font-medium">{workflow.workflow.category}</p>
									</div>
									<div class="bg-purple-50 p-3 rounded-lg">
										<Badge color="purple" class="mb-2">Schema Version</Badge>
										<p class="text-gray-700 font-medium">{workflow.schema_version}</p>
									</div>
									<div class="bg-yellow-50 p-3 rounded-lg">
										<Badge color="yellow" class="mb-2">Tags</Badge>
										<div class="flex flex-wrap gap-1">
											{#each workflow.workflow.tags as tag}
												<Badge color="dark" class="text-xs">{tag}</Badge>
											{/each}
										</div>
									</div>
								</div>
							</div> -->

							<!-- API Configuration -->
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
								<div class="bg-white rounded-lg p-4 border border-gray-200">
									<h5 class="font-semibold text-gray-800 mb-3 flex items-center">
										{t('settings_policy_workflow_configuration_api')}
									</h5>
									<div class="space-y-3">
										<div>
											<Badge color="dark" class="mb-1">Base URL</Badge>
											<code class="block bg-gray-100 p-2 rounded text-xs break-all">{workflow.configuration.api.base_url}</code>
										</div>
										<div class="grid grid-cols-2 gap-3">
											<div>
												<Badge color="dark" class="mb-1">Timeout</Badge>
												<p class="text-sm text-gray-600">{workflow.configuration.api.timeout_seconds}s</p>
											</div>
											<div>
												<Badge color="dark" class="mb-1">SSL Verify</Badge>
												<p class="text-sm text-gray-600">{workflow.configuration.api.ssl_verify ? 'Yes' : 'No'}</p>
											</div>
										</div>
										<div class="grid grid-cols-2 gap-3">
											<div>
												<Badge color="dark" class="mb-1">Max Retries</Badge>
												<p class="text-sm text-gray-600">{workflow.configuration.api.max_retries}</p>
											</div>
											<div>
												<Badge color="dark" class="mb-1">Retry Delay</Badge>
												<p class="text-sm text-gray-600">{workflow.configuration.api.retry_delay_seconds}s</p>
											</div>
										</div>
									</div>
								</div>

								<div class="bg-white rounded-lg p-4 border border-gray-200">
									<h5 class="font-semibold text-gray-800 mb-3 flex items-center">
										{t('settings_policy_workflow_configuration_execution')}
									</h5>
									<div class="space-y-5">
										<div>
											<Badge color="dark" class="mb-1">Execution Timeout</Badge>
											<p class="text-sm text-gray-600">{workflow.configuration.execution.timeout_minutes} minutes</p>
										</div>
										<div>
											<Badge color="dark" class="mb-1">Cache Status</Badge>
											<p class="text-sm text-gray-600">{workflow.configuration.execution.cache.enabled ? 'Enabled' : 'Disabled'}</p>
										</div>
										<div>
											<Badge color="dark" class="mb-1">Cache Duration</Badge>
											<p class="text-sm text-gray-600">{workflow.configuration.execution.cache.duration_minutes} minutes</p>
										</div>
										<!-- <div>
											<Badge color="dark" class="mb-1">Data Source Mode</Badge>
											<p class="text-sm text-gray-600">{workflow.configuration.data_source.mode}</p>
										</div> -->
									</div>
								</div>
							</div>

							<!-- Fixed Values -->
							{#if workflow.configuration.data_source.fixed_values && Object.keys(workflow.configuration.data_source.fixed_values).length > 0}
								<div class="bg-white rounded-lg p-4 border border-gray-200">
									<h5 class="font-semibold text-gray-800 mb-3 flex items-center">
										{t('settings_policy_workflow_configuration_dummy_data')}
									</h5>
									<pre class="bg-gray-100 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(workflow.configuration.data_source.fixed_values, null, 2)}</code></pre>
								</div>
							{/if}
						</div>

						<!-- WORKFLOW STEPS SECTION -->
						<div>
							<div class="flex items-center mb-6">
								<div class="bg-blue-100 p-2 rounded-lg mr-3">
									<LayersSolid class="w-6 h-6 text-blue-500" />
								</div>
								<div>
									<h3 class="text-xl font-bold text-gray-800">{t('settings_policy_workflow_step_title')}</h3>
									<!-- <p class="text-sm text-gray-600">Individual steps in the workflow execution sequence</p> -->
								</div>
								<div class="ml-auto">
									<Badge color="dark">{workflow.steps.length} {t('settings_policy_workflow_step_total')}</Badge>
								</div>
							</div>

							<Timeline>
								{#each workflow.steps as step, stepIndex}
									<TimelineItem date="{t('settings_policy_workflow_step_number')} {stepIndex + 1}">
										<Card class="mt-3 bg-white border-0 border-l-4 border-blue-500" size="xl">
											<div class="space-y-4">
												<!-- Step Header -->
												<div class="border-b border-gray-100 pb-3">
													<div class="flex items-start justify-between">
														<div class="space-y-1">
															<Badge color="green"># {step.id}</Badge>
															<!-- <p class="text-sm text-gray-600 mt-1">{step.description}</p> -->
															<h5 class="font-semibold text-gray-800 text-lg">{step.description}</h5>
														</div>
														<!-- <Badge color="indigo" class="text-xs">Type: {step.type}</Badge> -->
													</div>
													<div class="flex items-center space-x-2 mt-3">
														<!-- <Badge color={colorScheme.badgeColor}>{step.config.method}</Badge>
														{#if step.config.retry}
															<Badge color="dark">Max Retries: {step.config.retry.max_attempts}</Badge>
															<Badge color="purple">Retry Delay: {step.config.retry.delay_seconds}s</Badge>
														{/if} -->
														{#if step.depends_on && step.depends_on.length > 0}
															<span class="text-xs text-gray-900">Depends on: </span>
															{#each step.depends_on as dependency}
															 	<Badge color="green"># {dependency}</Badge>
															{/each}
														{/if}
													</div>
												</div>

												<!-- Step Configuration Details -->
												<div class="grid grid-cols-1 gap-4">
													<!-- Endpoint -->
													<div>
														<Badge color="dark" class="mb-2">Endpoint</Badge>
														<code class="block bg-gray-100 p-2 rounded text-xs text-gray-900 break-all"><Badge color="blue">{step.config.method}</Badge> {step.config.endpoint}</code>
													</div>

													<!-- Headers (if present) -->
													{#if step.config.headers}
														<div>
															<Badge color="dark" class="mb-2">Headers</Badge>
															<pre class="bg-gray-100 p-3 rounded text-xs text-gray-900 overflow-x-auto"><code>{JSON.stringify(step.config.headers, null, 2)}</code></pre>
														</div>
													{/if}

													<!-- Request Body -->
													{#if step.config.request_body}
														<div>
															<Badge color="dark" class="mb-2">Request Body</Badge>
															<pre class="bg-gray-100 p-3 rounded text-xs text-gray-900 overflow-x-auto"><code>{JSON.stringify(step.config.request_body, null, 2)}</code></pre>
														</div>
													{/if}

													<!-- Response Extraction -->
													{#if step.config.response_extraction}
														<div>
															<Badge color="dark" class="mb-2">Response Extraction</Badge>
															<pre class="bg-gray-100 p-3 rounded text-xs text-gray-900 overflow-x-auto"><code>{JSON.stringify(step.config.response_extraction, null, 2)}</code></pre>
														</div>
													{/if}

													<!-- Validation (if present) -->
													{#if step.config.validation}
														<div>
															<Badge color="dark" class="mb-2">Validation Rules</Badge>
															<div class="space-y-2">
																{#each step.config.validation.rules as rule}
																	<div class="bg-gray-100 p-3 rounded">
																		<div class="grid grid-cols-3 gap-2 text-xs text-gray-900">
																			<!-- <div><strong>Rule ID:</strong> {rule.id}</div> -->
																			<!-- <div><strong>Type:</strong> {rule.type}</div> -->
																			{#if rule.path}
																				<div><strong>Path:</strong> {rule.path}</div>
																			{/if}
																			{#if rule.operator}
																				<div><strong>Operator:</strong> {rule.operator}</div>
																			{/if}
																			{#if rule.value}
																				<div><strong>Expected Value:</strong> {rule.value}</div>
																			{/if}
																		</div>
																		<div class="mt-2 text-xs text-gray-900">
																			<strong>Error Message:</strong> {rule.error_message}
																		</div>
																		<!-- {#if rule.warning_only}
																			<Badge color="yellow" class="mt-2">Warning Only</Badge>
																		{/if} -->
																	</div>
																{/each}
															</div>
														</div>
													{/if}
												</div>
											</div>
										</Card>
									</TimelineItem>
								{/each}
								<TimelineItem date="{t('settings_policy_workflow_step_end')}" />
							</Timeline>
						</div>
					</div>
				</TabItem>
			{/each}
		</Tabs>
	{/if}
</div>




